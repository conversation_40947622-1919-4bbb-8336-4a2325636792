# 数据库配置
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=knowledge_base
DATABASE_URL=postgresql://postgres:password@localhost:5432/knowledge_base

# Milvus向量数据库配置
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_COLLECTION_NAME=documents

# Redis配置
REDIS_URL=redis://localhost:6379

# JWT认证配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 文件上传配置
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=pdf,doc,docx,txt,md

# AI模型配置
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
RERANK_MODEL=cross-encoder/ms-marco-MiniLM-L-6-v2
CHUNK_SIZE=512
CHUNK_OVERLAP=50

# 应用配置
APP_NAME=Knowledge Base Assistant
APP_VERSION=1.0.0
DEBUG=True
CORS_ORIGINS=http://localhost:5173,http://127.0.0.1:5173

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=app.log
